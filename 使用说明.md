# pCloud文件批量提取工具使用说明

## 功能介绍

这个工具可以批量提取您pCloud账户中的所有文件名和对应的分享链接，并将结果保存为Excel文件。

## 安装依赖

首先安装必要的Python包：

```bash
pip install -r requirements.txt
```

## 使用方法

### 运行程序

```bash
python pcloud_extract_links.py
```

### 认证方式

程序支持两种认证方式：

#### 1. 用户名密码认证（推荐）
- 直接使用您的pCloud用户名和密码
- 适合个人使用

#### 2. OAuth2认证
- 需要先在pCloud开发者页面申请API密钥
- 适合开发应用程序使用

## 功能说明

### 扫描范围

程序支持两种扫描模式：
1. **全盘扫描**：扫描整个pCloud中的所有文件和文件夹
2. **指定路径**：通过交互式文件夹浏览器选择特定路径进行扫描

### 提取内容

程序会提取以下信息：
- 文件名
- 文件路径
- 文件大小（MB）
- 创建时间
- 修改时间
- 分享链接（如果存在）
- 链接ID
- 下载次数
- 流量统计（MB格式）
- 文件ID

### 分享链接处理

**重大突破：** 程序现在支持自动创建分享链接！

处理逻辑：
1. 首先匹配现有的分享链接
2. 对于没有分享链接的文件，询问是否自动创建（默认创建）
3. 文件夹分享链接会标记为"(文件夹链接)"

### 输出文件

程序会生成一个Excel文件，包含：
- 所有文件的详细信息
- 自动调整的列宽
- 统计信息（总文件数、有分享链接的文件数、总文件大小）

## 注意事项

1. **API限制**：无法直接为文件创建分享链接，只能获取现有的分享链接
2. **网络要求**：需要稳定的网络连接访问pCloud API
3. **大量文件**：如果您的pCloud中有大量文件，扫描过程可能需要较长时间
4. **权限要求**：需要您的pCloud账户登录权限

## 故障排除

### 连接失败
- 检查用户名和密码是否正确
- 确认网络连接正常
- 验证pCloud服务是否可访问

### 没有找到分享链接
- 确认您的pCloud中确实存在公共分享链接
- 检查文件权限设置

### Excel文件保存失败
- 确认有足够的磁盘空间
- 检查文件路径权限
- 确保没有其他程序占用同名文件

## 示例输出

Excel文件将包含如下列：

| 文件名 | 文件路径 | 文件大小(MB) | 创建时间 | 修改时间 | 分享链接 | 链接ID | 下载次数 | 流量 | 文件ID |
|--------|----------|--------------|----------|----------|----------|--------|----------|------|--------|
| 示例 | /文档/示例.pdf | 2.5 | 2024-01-01 10:00:00 | 2024-01-01 10:00:00 | https://u.pcloud.link/publink/show?code=XXX | 123 | 5 | 1.00 MB | 456 |

> **注意**：文件名列已自动去除扩展名，Excel文件统一保存到 `/Users/<USER>/Desktop/Input/pCloud链接.xlsx`

## 技术支持

如果遇到问题，请检查：
1. Python版本（建议3.9+）
2. 依赖包是否正确安装
3. pCloud账户状态是否正常

## 安全提示

- 请妥善保管您的pCloud账户信息
- 建议在私人网络环境下运行此工具
- 生成的Excel文件可能包含敏感信息，请注意保密 