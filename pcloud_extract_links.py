#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
pCloud文件批量提取工具
功能：提取pCloud中的所有文件名和对应的分享链接，保存为Excel文件
"""

import os
import sys
import pandas as pd
from datetime import datetime
import json

# 添加src目录到Python路径，以便导入pcloud模块
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from pcloud import PyCloud

# 预设账户信息 (如果不为空，将自动使用这些信息登录)
PRESET_USERNAME = "<EMAIL>"
PRESET_PASSWORD = "abcd12345678"
# 安全提示：将账户信息硬编码在程序中存在安全风险，请确保：
# 1. 不要将此文件分享给他人
# 2. 不要上传到公共代码仓库
# 3. 建议在使用后清空这些信息



class PCloudFileExtractor:
    """pCloud文件提取器"""
    
    def __init__(self, username=None, password=None, oauth2=False):
        """
        初始化pCloud连接
        
        Args:
            username (str): pCloud用户名（如果使用OAuth2则为空）
            password (str): pCloud密码（如果使用OAuth2则为access_token）
            oauth2 (bool): 是否使用OAuth2认证
        """
        self.username = username
        self.password = password
        self.oauth2 = oauth2
        self.pc = None
        self.all_files = []
        self.public_links = {}
        
    def connect(self):
        """连接到pCloud"""
        try:
            print("正在连接到pCloud...")
            self.pc = PyCloud(
                username=self.username or "",
                password=self.password or "",
                oauth2=self.oauth2
            )
            
            # 测试连接
            user_info = self.pc.userinfo()
            if 'result' in user_info and user_info['result'] != 0:
                raise Exception(f"认证失败: {user_info}")
            
            print(f"连接成功! 用户: {user_info.get('email', 'Unknown')}")
            return True
            
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    def get_all_files(self, folder_id=0, path="/"):
        """
        递归获取所有文件
        
        Args:
            folder_id (int): 文件夹ID，0为根目录
            path (str): 当前路径
        """
        try:
            print(f"正在扫描文件夹: {path} (ID: {folder_id})")
            # 确保folderid参数正确传递
            if folder_id == 0:
                # 根目录使用folderid=0
                response = self.pc.listfolder(folderid=0)
            else:
                # 其他文件夹，需要处理不同格式的ID
                folder_id_str = str(folder_id)
                if folder_id_str.startswith('d'):
                    # 如果是d开头的文件夹ID，去掉d前缀
                    clean_folder_id = int(folder_id_str[1:])
                    response = self.pc.listfolder(folderid=clean_folder_id)
                else:
                    # 直接使用数字ID
                    response = self.pc.listfolder(folderid=int(folder_id))
            
            if 'result' in response and response['result'] != 0:
                print(f"获取文件夹内容失败: {response}")
                return
            
            contents = response.get('metadata', {}).get('contents', [])
            
            for item in contents:
                item_name = item.get('name', '')
                item_id = item.get('id', 0)
                item_path = os.path.join(path, item_name).replace('\\', '/')
                
                if item.get('isfolder', False):
                    # 这是一个文件夹，递归处理
                    self.get_all_files(item_id, item_path)
                else:
                    # 这是一个文件
                    file_info = {
                        'name': item_name,
                        'path': item_path,
                        'size': item.get('size', 0),
                        'created': item.get('created', ''),
                        'modified': item.get('modified', ''),
                        'id': item_id,
                        'parentfolderid': item.get('parentfolderid', 0),
                        'fileid': item.get('fileid', item_id),  # 使用API返回的fileid
                        'share_link': '',  # 将在后面填充
                        'link_id': '',
                        'link_expire': ''
                    }
                    self.all_files.append(file_info)
                    
        except Exception as e:
            print(f"扫描文件夹 {path} 时出错: {e}")
    
    def browse_folders(self, folder_id=0, path="/", level=0):
        """
        浏览文件夹结构，让用户选择要扫描的路径
        
        Args:
            folder_id (int): 当前文件夹ID
            path (str): 当前路径
            level (int): 层级深度（用于缩进显示）
        
        Returns:
            tuple: (selected_folder_id, selected_path) 或 (None, None) 如果取消
        """
        try:
            # 获取当前文件夹内容
            if folder_id == 0:
                response = self.pc.listfolder(folderid=0)
            else:
                folder_id_str = str(folder_id)
                if folder_id_str.startswith('d'):
                    clean_folder_id = int(folder_id_str[1:])
                    response = self.pc.listfolder(folderid=clean_folder_id)
                else:
                    response = self.pc.listfolder(folderid=int(folder_id))
            
            if 'result' in response and response['result'] != 0:
                print(f"❌ 无法访问文件夹: {path}")
                return None, None
            
            contents = response.get('metadata', {}).get('contents', [])
            folders = [item for item in contents if item.get('isfolder', False)]
            files_count = len([item for item in contents if not item.get('isfolder', False)])
            
            # 显示当前路径信息
            indent = "  " * level
            print(f"\n{indent}📁 当前路径: {path}")
            print(f"{indent}   包含 {len(folders)} 个子文件夹，{files_count} 个文件")
            
            if level == 0:
                print(f"\n🎯 路径选择选项:")
                print(f"  0. 选择当前路径 ({path})")
                if folders:
                    print(f"  📁 子文件夹:")
                    for i, folder in enumerate(folders[:10], 1):  # 最多显示10个
                        print(f"     {i}. {folder.get('name', 'Unknown')}/")
                    if len(folders) > 10:
                        print(f"     ... 还有 {len(folders) - 10} 个文件夹")
                print(f"  q. 返回上级或退出")
            else:
                print(f"\n选择操作:")
                print(f"  0. 选择当前路径 ({path})")
                if folders:
                    for i, folder in enumerate(folders[:5], 1):  # 显示前5个
                        print(f"  {i}. 进入 {folder.get('name', 'Unknown')}/")
                print(f"  b. 返回上级")
                print(f"  q. 退出选择")
            
            if level == 0:
                choice = input(f"\n请选择 (0-{min(len(folders), 10)}, q): ").strip().lower()
            else:
                choice = input(f"\n请选择 (0-{min(len(folders), 5)}, b, q): ").strip().lower()
            
            if choice == 'q':
                return None, None
            elif choice == 'b' and level > 0:
                return 'back', 'back'
            elif choice == '0':
                return folder_id, path
            elif choice.isdigit():
                idx = int(choice) - 1
                max_folders = 10 if level == 0 else 5
                if 0 <= idx < min(len(folders), max_folders):
                    selected_folder = folders[idx]
                    selected_id = selected_folder.get('id', 0)
                    selected_name = selected_folder.get('name', 'Unknown')
                    selected_path = f"{path.rstrip('/')}/{selected_name}"
                    
                    # 递归进入子文件夹
                    result = self.browse_folders(selected_id, selected_path, level + 1)
                    if result == ('back', 'back'):
                        # 返回当前层级
                        return self.browse_folders(folder_id, path, level)
                    else:
                        return result
                else:
                    print("❌ 无效的选择")
                    return self.browse_folders(folder_id, path, level)
            else:
                print("❌ 无效的选择")
                return self.browse_folders(folder_id, path, level)
                
        except Exception as e:
            print(f"❌ 浏览文件夹时出错: {e}")
            return None, None
    
    def get_public_links(self):
        """获取所有公共分享链接"""
        try:
            print("正在获取公共分享链接...")
            response = self.pc.listpublinks()
            
            if 'result' in response and response['result'] != 0:
                print(f"获取公共链接失败: {response}")
                return
            
            links = response.get('publinks', [])
            print(f"找到 {len(links)} 个公共链接")
            
            # 创建链接映射（按文件ID或文件夹ID）
            processed_count = 0
            for link in links:
                link_id = link.get('linkid', '')
                link_url = f"https://u.pcloud.link/publink/show?code={link.get('code', '')}"
                
                # 检查链接类型 - 支持顶级和metadata中的fileid/folderid
                file_id = None
                folder_id = None
                
                # 检查顶级fileid/folderid
                if 'fileid' in link:
                    file_id = link['fileid']
                elif 'folderid' in link:
                    folder_id = link['folderid']
                # 检查metadata中的fileid/folderid
                elif 'metadata' in link:
                    metadata = link['metadata']
                    if 'fileid' in metadata:
                        file_id = metadata['fileid']
                    elif 'folderid' in metadata:
                        folder_id = metadata['folderid']
                
                if file_id is not None:
                    # 文件链接
                    self.public_links[f"file_{file_id}"] = {
                        'url': link_url,
                        'linkid': link_id,
                        'expire': link.get('expire', ''),
                        'created': link.get('created', ''),
                        'downloads': link.get('downloads', 0),
                        'traffic': link.get('traffic', 0)
                    }
                    processed_count += 1
                elif folder_id is not None:
                    # 文件夹链接
                    self.public_links[f"folder_{folder_id}"] = {
                        'url': link_url,
                        'linkid': link_id,
                        'expire': link.get('expire', ''),
                        'created': link.get('created', ''),
                        'downloads': link.get('downloads', 0),
                        'traffic': link.get('traffic', 0)
                    }
                    processed_count += 1
            
            print(f"成功匹配 {processed_count} 个现有分享链接")
                    
        except Exception as e:
            print(f"获取公共链接时出错: {e}")
    
    def create_missing_links(self, create_links=False):
        """为没有分享链接的文件创建新的分享链接"""
        if not create_links:
            return
            
        files_without_links = [f for f in self.all_files if not f['share_link']]
        if not files_without_links:
            print("✅ 所有文件都已有分享链接")
            return
            
        print(f"🔗 发现 {len(files_without_links)} 个文件没有分享链接")
        print("正在为这些文件创建分享链接...")
        
        created_count = 0
        failed_count = 0
        
        for i, file_info in enumerate(files_without_links, 1):
            try:
                print(f"  [{i}/{len(files_without_links)}] 正在为 '{file_info['name']}' 创建分享链接...")
                
                # 使用path创建公共链接（测试验证这种方法有效）
                file_path = file_info['path']
                print(f"    📁 文件路径: {file_path}")
                
                response = self.pc.getfilepublink(path=file_path)
                
                if 'result' in response and response['result'] == 0:
                    # 成功创建链接
                    link_code = response.get('code', '')
                    link_id = response.get('linkid', '')
                    link_url = response.get('link', f"https://u.pcloud.link/publink/show?code={link_code}")
                    
                    # 更新文件信息
                    file_info['share_link'] = link_url
                    file_info['link_id'] = str(link_id)
                    file_info['link_expire'] = response.get('expire', '')
                    file_info['link_downloads'] = response.get('downloads', 0)
                    file_info['link_traffic'] = response.get('traffic', 0)
                    
                    created_count += 1
                    print(f"    ✅ 成功创建: {link_url}")
                else:
                    failed_count += 1
                    error_msg = response.get('error', response.get('result', '未知错误'))
                    print(f"    ❌ 创建失败: {error_msg}")
                    
            except Exception as e:
                failed_count += 1
                print(f"    ❌ 创建失败: {e}")
                
        print(f"\n📊 分享链接创建完成:")
        print(f"  ✅ 成功创建: {created_count} 个")
        print(f"  ❌ 创建失败: {failed_count} 个")
    
    def match_files_with_links(self):
        """将文件与分享链接匹配"""
        print("正在匹配文件与分享链接...")
        matched_count = 0
        
        for file_info in self.all_files:
            file_id = file_info['id']
            
            # 尝试直接匹配文件ID，支持多种格式
            file_key = f"file_{file_id}"
            file_key_clean = None
            
            # 如果文件ID以'f'开头，也尝试去掉'f'前缀的版本
            if str(file_id).startswith('f'):
                clean_id = str(file_id)[1:]
                file_key_clean = f"file_{clean_id}"
            
            if file_key in self.public_links:
                link_info = self.public_links[file_key]
            elif file_key_clean and file_key_clean in self.public_links:
                link_info = self.public_links[file_key_clean]
            else:
                link_info = None
            
            if link_info:
                file_info['share_link'] = link_info['url']
                file_info['link_id'] = link_info['linkid']
                file_info['link_expire'] = link_info['expire']
                file_info['link_downloads'] = link_info['downloads']
                file_info['link_traffic'] = link_info['traffic']
                matched_count += 1
            else:
                # 尝试匹配父文件夹的链接
                parent_folder_id = file_info['parentfolderid']
                folder_key = f"folder_{parent_folder_id}"
                if folder_key in self.public_links:
                    link_info = self.public_links[folder_key]
                    file_info['share_link'] = f"{link_info['url']} (文件夹链接)"
                    file_info['link_id'] = link_info['linkid']
                    file_info['link_expire'] = link_info['expire']
                    file_info['link_downloads'] = link_info['downloads']
                    file_info['link_traffic'] = link_info['traffic']
                    matched_count += 1
        
        print(f"成功匹配 {matched_count} 个文件的分享链接")
        
        # 显示没有分享链接的文件数量
        files_without_links = len([f for f in self.all_files if not f['share_link']])
        if files_without_links > 0:
            print(f"🔗 发现 {files_without_links} 个文件没有分享链接")
    
    def save_to_excel(self, filename="pCloud链接.xlsx"):
        """将结果保存到Excel文件"""
        try:
            import os
            
            # 固定输出路径
            output_dir = "/Users/<USER>/Desktop/Input"
            
            # 确保目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 构建完整路径
            full_path = os.path.join(output_dir, filename).replace('\\', '/')
            
            print(f"正在保存到Excel文件: {full_path}")
            
            # 准备数据
            df_data = []
            for file_info in self.all_files:
                # 转换时间戳为可读格式
                created_time = ""
                modified_time = ""
                
                if file_info.get('created'):
                    try:
                        created_time = datetime.fromtimestamp(file_info['created']).strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        created_time = str(file_info['created'])
                        
                if file_info.get('modified'):
                    try:
                        modified_time = datetime.fromtimestamp(file_info['modified']).strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        modified_time = str(file_info['modified'])
                
                # 格式化文件大小
                size_mb = file_info['size'] / (1024 * 1024) if file_info['size'] > 0 else 0
                

                
                # 格式化流量大小
                traffic_mb = ""
                if file_info.get('link_traffic') and file_info['link_traffic'] > 0:
                    traffic_bytes = file_info['link_traffic']
                    traffic_mb = f"{traffic_bytes / (1024 * 1024):.2f} MB"
                elif file_info.get('link_traffic') == 0:
                    traffic_mb = "0 MB"
                
                # 去掉文件扩展名
                filename_without_ext = file_info['name']
                if '.' in filename_without_ext:
                    filename_without_ext = '.'.join(filename_without_ext.split('.')[:-1])
                
                df_data.append({
                    '文件名': filename_without_ext,
                    # '文件路径': file_info['path'],
                    # '文件大小(MB)': round(size_mb, 2),
                    # '创建时间': created_time,
                    # '修改时间': modified_time,
                    '分享链接': file_info['share_link'],
                    # '链接ID': file_info['link_id'],
                    # '下载次数': file_info.get('link_downloads', 0),
                    # '流量': traffic_mb,
                    # '文件ID': file_info['id']
                })
            
            # 创建DataFrame
            df = pd.DataFrame(df_data)
            
            # 保存到Excel
            with pd.ExcelWriter(full_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='文件列表', index=False)
                
                # 获取工作簿和工作表
                workbook = writer.book
                worksheet = writer.sheets['文件列表']
                
                # 调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            print(f"成功保存 {len(df_data)} 个文件的信息到 {full_path}")
            
            # 显示统计信息
            total_files = len(df_data)
            files_with_links = len([f for f in self.all_files if f['share_link']])
            total_size_gb = sum([f['size'] for f in self.all_files]) / (1024 * 1024 * 1024)
            
            print(f"\n=== 统计信息 ===")
            print(f"总文件数: {total_files}")
            print(f"有分享链接的文件: {files_with_links}")
            print(f"总文件大小: {total_size_gb:.2f} GB")
            
        except Exception as e:
            print(f"保存Excel文件时出错: {e}")
    
    def extract(self, output_filename="pCloud链接.xlsx"):
        """执行完整的提取流程"""
        print("=== pCloud文件批量提取工具 ===\n")
        
        # 1. 连接pCloud
        if not self.connect():
            return False
        
        # 2. 选择扫描路径
        print(f"\n选择扫描范围...")
        scan_choice = input("扫描范围选择:\n1. 扫描整个pCloud (默认)\n2. 选择指定文件夹\n请选择 (1/2，默认1): ").strip()
        
        if scan_choice == '2':
            print(f"\n📁 浏览文件夹结构，选择要扫描的路径:")
            selected_folder_id, selected_path = self.browse_folders()
            
            if selected_folder_id is None:
                print("❌ 已取消路径选择，将扫描整个pCloud")
                selected_folder_id, selected_path = 0, "/"
            else:
                print(f"✅ 已选择路径: {selected_path}")
        else:
            selected_folder_id, selected_path = 0, "/"
            print(f"✅ 将扫描整个pCloud")
        
        # 3. 获取所有文件
        print(f"\n开始扫描文件...")
        self.get_all_files(selected_folder_id, selected_path)
        print(f"共找到 {len(self.all_files)} 个文件")
        
        # 4. 获取公共链接
        print(f"\n开始获取分享链接...")
        self.get_public_links()

        
        # 5. 匹配文件和链接
        print(f"\n开始匹配文件和链接...")
        self.match_files_with_links()
        
        # 6. 询问是否创建缺失的分享链接
        files_without_links = len([f for f in self.all_files if not f['share_link']])
        create_links = False
        
        if files_without_links > 0:
            print(f"\n🔗 检测到 {files_without_links} 个文件没有分享链接")
            create_choice = input("是否为这些文件创建新的分享链接？(y/n，默认y): ").strip().lower()
            
            if create_choice != 'n':
                create_links = True
                print("✅ 将为没有分享链接的文件创建新的分享链接")
            else:
                print("⏭️ 跳过创建分享链接")
        
        # 7. 创建缺失的分享链接（如果用户选择）
        if create_links:
            print(f"\n开始创建分享链接...")
            self.create_missing_links(create_links=True)
        
        # 8. 保存到Excel
        print(f"\n开始保存到Excel...")
        self.save_to_excel(output_filename)
        
        # 构建完整路径用于显示
        import os
        output_dir = "/Users/<USER>/Desktop/Input"
        full_output_path = os.path.join(output_dir, output_filename)
        print(f"\n✅ 提取完成！结果已保存到: {full_output_path}")
        return True


def main():
    """主函数"""
    print("=== pCloud文件批量提取工具 ===")
    print("此工具可以提取您pCloud中的所有文件名和对应的分享链接")
    print()
    
    # 检查是否有预设账户信息
    if PRESET_USERNAME and PRESET_PASSWORD:
        print(f"🔐 检测到预设账户信息: {PRESET_USERNAME}")
        use_preset = input("是否使用预设账户？(y/n，默认y): ").strip().lower()
        
        if use_preset != 'n':
            print("✅ 使用预设账户信息")
            extractor = PCloudFileExtractor(username=PRESET_USERNAME, password=PRESET_PASSWORD, oauth2=False)
        else:
            print("⚙️ 手动选择认证方式")
            extractor = get_manual_auth()
    else:
        print("⚙️ 请选择认证方式")
        extractor = get_manual_auth()
    
    if extractor is None:
        return
    
    # 设置输出文件名
    output_file = input("\n请输入输出Excel文件名 (默认: pCloud链接.xlsx): ").strip()
    if not output_file:
        output_file = "pCloud链接.xlsx"
    
    if not output_file.endswith('.xlsx'):
        output_file += '.xlsx'
    
    # 执行提取
    print(f"\n开始提取...")
    success = extractor.extract(output_file)
    
    if success:
        print(f"\n🎉 提取成功完成！")
        print(f"📁 结果文件: {output_file}")
    else:
        print(f"\n❌ 提取失败，请检查网络连接和认证信息")


def get_manual_auth():
    """获取手动认证信息"""
    # 选择认证方式
    auth_method = input("请选择认证方式:\n1. 用户名密码\n2. OAuth2 (access_token)\n请输入选择 (1 or 2): ").strip()
    
    if auth_method == "1":
        # 用户名密码认证
        username = input("请输入pCloud用户名: ").strip()
        password = input("请输入pCloud密码: ").strip()
        
        if not username or not password:
            print("❌ 用户名和密码不能为空")
            return None
            
        return PCloudFileExtractor(username=username, password=password, oauth2=False)
        
    elif auth_method == "2":
        # OAuth2认证
        print("\n使用OAuth2认证需要先获取access_token")
        print("请访问pCloud开发者页面获取client_id和client_secret")
        
        client_id = input("请输入client_id: ").strip()
        client_secret = input("请输入client_secret: ").strip()
        
        if not client_id or not client_secret:
            print("❌ client_id和client_secret不能为空")
            return None
        
        try:
            print("\n正在启动OAuth2认证流程...")
            pc_oauth = PyCloud.oauth2_authorize(client_id, client_secret)
            return PCloudFileExtractor(username="", password=pc_oauth.access_token, oauth2=True)
        except Exception as e:
            print(f"❌ OAuth2认证失败: {e}")
            return None
    else:
        print("❌ 无效的选择")
        return None


if __name__ == "__main__":
    main() 