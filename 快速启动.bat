@echo off
chcp 65001 >nul
echo ========================================
echo pCloud文件批量提取工具 - 快速启动
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python，请先安装Python 3.9或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 正在安装必要的依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖包安装失败，请检查网络连接或手动安装
    pause
    exit /b 1
)

echo ✅ 依赖包安装完成
echo.

echo 正在启动pCloud文件提取工具...
echo.
python pcloud_extract_links.py

echo.
echo 程序执行完成，按任意键退出...
pause >nul 