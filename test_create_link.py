#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试创建分享链接功能
"""

import os
import sys
import json

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from pcloud import PyCloud

# 预设账户信息
USERNAME = "<EMAIL>"
PASSWORD = "abcd12345678"

def test_connection():
    """测试连接"""
    print("🔐 正在连接pCloud...")
    try:
        pc = PyCloud(username=USERNAME, password=PASSWORD, oauth2=False)
        user_info = pc.userinfo()
        
        if 'result' in user_info and user_info['result'] != 0:
            print(f"❌ 认证失败: {user_info}")
            return None
        
        print(f"✅ 连接成功! 用户: {user_info.get('email', 'Unknown')}")
        return pc
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return None

def test_list_files(pc):
    """测试获取文件列表"""
    print("\n📁 正在获取根目录文件列表...")
    try:
        response = pc.listfolder(folderid=0)
        
        if 'result' in response and response['result'] != 0:
            print(f"❌ 获取文件列表失败: {response}")
            return []
        
        contents = response.get('metadata', {}).get('contents', [])
        files = [item for item in contents if not item.get('isfolder', False)]
        
        print(f"✅ 找到 {len(files)} 个文件:")
        for i, file_item in enumerate(files[:5], 1):  # 只显示前5个
            print(f"  {i}. {file_item.get('name', 'Unknown')} (ID: {file_item.get('id', 'N/A')})")
        
        return files
        
    except Exception as e:
        print(f"❌ 获取文件列表失败: {e}")
        return []

def test_create_link(pc, file_item):
    """测试创建分享链接"""
    file_name = file_item.get('name', 'Unknown')
    file_id = file_item.get('id', '')
    file_path = f"/{file_name}"
    
    print(f"\n🔗 正在为文件 '{file_name}' 创建分享链接...")
    print(f"   文件ID: {file_id}")
    print(f"   文件路径: {file_path}")
    
    # 方法1: 使用path
    print("\n方法1: 使用path参数")
    try:
        response = pc.getfilepublink(path=file_path)
        print(f"✅ 使用path成功创建链接:")
        print(f"   Response: {json.dumps(response, indent=2, ensure_ascii=False)}")
        return True
    except Exception as e:
        print(f"❌ 使用path失败: {e}")
    
    # 方法2: 使用fileid
    print("\n方法2: 使用fileid参数")
    try:
        response = pc.getfilepublink(fileid=file_id)
        print(f"✅ 使用fileid成功创建链接:")
        print(f"   Response: {json.dumps(response, indent=2, ensure_ascii=False)}")
        return True
    except Exception as e:
        print(f"❌ 使用fileid失败: {e}")
    
    # 方法3: 使用转换后的fileid
    print("\n方法3: 使用转换后的fileid参数")
    try:
        # 处理fileid格式
        if str(file_id).startswith('f'):
            clean_file_id = int(str(file_id)[1:])
        else:
            clean_file_id = int(file_id)
            
        print(f"   转换后的ID: {clean_file_id}")
        response = pc.getfilepublink(fileid=clean_file_id)
        print(f"✅ 使用转换后的fileid成功创建链接:")
        print(f"   Response: {json.dumps(response, indent=2, ensure_ascii=False)}")
        return True
    except Exception as e:
        print(f"❌ 使用转换后的fileid失败: {e}")
    
    return False

def main():
    """主函数"""
    print("=== pCloud创建分享链接测试 ===")
    
    # 1. 测试连接
    pc = test_connection()
    if not pc:
        return
    
    # 2. 获取文件列表
    files = test_list_files(pc)
    if not files:
        print("❌ 没有找到文件，无法测试")
        return
    
    # 3. 选择一个文件测试
    test_file = files[0]  # 使用第一个文件
    print(f"\n🎯 选择测试文件: {test_file.get('name', 'Unknown')}")
    
    # 4. 测试创建链接
    success = test_create_link(pc, test_file)
    
    if success:
        print(f"\n🎉 测试成功！分享链接创建功能正常工作")
    else:
        print(f"\n❌ 测试失败！需要进一步调试")

if __name__ == "__main__":
    main() 