@echo off
chcp 65001 >nul
echo ========================================
echo pCloud文件批量提取工具 - 预设账户版
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python，请先安装Python 3.9或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 正在安装必要的依赖包...
pip install -r requirements.txt >nul 2>&1
if errorlevel 1 (
    echo ❌ 依赖包安装失败，请检查网络连接
    echo 正在尝试手动安装...
    pip install requests requests-toolbelt pandas openpyxl
)

echo ✅ 依赖包准备完成
echo.

echo 🚀 正在启动pCloud文件提取工具...
echo 📧 预设账户: <EMAIL>
echo 🔐 将自动使用预设的账户信息登录
echo.

(echo y & echo 1 & echo & echo) | python pcloud_extract_links.py

echo.
echo 程序执行完成，按任意键退出...
pause >nul 