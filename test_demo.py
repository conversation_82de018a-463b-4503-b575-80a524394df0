#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
pCloud文件批量提取工具 - 演示脚本
用于测试Excel导出功能，不需要真实的pCloud账户
"""

import pandas as pd
from datetime import datetime
import time

def create_demo_data():
    """创建演示数据"""
    print("正在生成演示数据...")
    
    demo_files = [
        {
            'name': '工作报告.pdf',
            'path': '/文档/工作/工作报告.pdf',
            'size': 2.5 * 1024 * 1024,  # 2.5MB
            'created': int(time.time() - 86400 * 30),  # 30天前
            'modified': int(time.time() - 86400 * 5),  # 5天前
            'id': 12345,
            'share_link': 'https://u.pcloud.link/publink/show?code=XYZ123',
            'link_id': 'link_001',
            'link_expire': '2024-12-31',
            'link_downloads': 15,
            'link_traffic': 1024000
        },
        {
            'name': '家庭照片.jpg',
            'path': '/照片/2024/家庭照片.jpg',
            'size': 5.2 * 1024 * 1024,  # 5.2MB
            'created': int(time.time() - 86400 * 60),  # 60天前
            'modified': int(time.time() - 86400 * 60),  # 60天前
            'id': 12346,
            'share_link': '',  # 没有分享链接
            'link_id': '',
            'link_expire': '',
            'link_downloads': 0,
            'link_traffic': 0
        },
        {
            'name': '项目文档.docx',
            'path': '/文档/项目/项目文档.docx',
            'size': 1.8 * 1024 * 1024,  # 1.8MB
            'created': int(time.time() - 86400 * 10),  # 10天前
            'modified': int(time.time() - 86400 * 2),  # 2天前
            'id': 12347,
            'share_link': 'https://u.pcloud.link/publink/show?code=ABC456 (文件夹链接)',
            'link_id': 'link_002',
            'link_expire': '',
            'link_downloads': 3,
            'link_traffic': 512000
        },
        {
            'name': '音乐文件.mp3',
            'path': '/音乐/流行/音乐文件.mp3',
            'size': 8.5 * 1024 * 1024,  # 8.5MB
            'created': int(time.time() - 86400 * 100),  # 100天前
            'modified': int(time.time() - 86400 * 100),  # 100天前
            'id': 12348,
            'share_link': 'https://u.pcloud.link/publink/show?code=DEF789',
            'link_id': 'link_003',
            'link_expire': '2025-06-30',
            'link_downloads': 45,
            'link_traffic': 8192000
        },
        {
            'name': '视频教程.mp4',
            'path': '/视频/教程/视频教程.mp4',
            'size': 125.6 * 1024 * 1024,  # 125.6MB
            'created': int(time.time() - 86400 * 15),  # 15天前
            'modified': int(time.time() - 86400 * 15),  # 15天前
            'id': 12349,
            'share_link': '',  # 没有分享链接
            'link_id': '',
            'link_expire': '',
            'link_downloads': 0,
            'link_traffic': 0
        }
    ]
    
    return demo_files

def save_demo_to_excel(demo_files, filename="demo_pcloud_files.xlsx"):
    """将演示数据保存到Excel文件"""
    try:
        print(f"正在保存演示数据到Excel文件: {filename}")
        
        # 准备数据
        df_data = []
        for file_info in demo_files:
            # 转换时间戳为可读格式
            created_time = ""
            modified_time = ""
            
            if file_info.get('created'):
                try:
                    created_time = datetime.fromtimestamp(file_info['created']).strftime('%Y-%m-%d %H:%M:%S')
                except:
                    created_time = str(file_info['created'])
                    
            if file_info.get('modified'):
                try:
                    modified_time = datetime.fromtimestamp(file_info['modified']).strftime('%Y-%m-%d %H:%M:%S')
                except:
                    modified_time = str(file_info['modified'])
            
            # 格式化文件大小
            size_mb = file_info['size'] / (1024 * 1024) if file_info['size'] > 0 else 0
            
            df_data.append({
                '文件名': file_info['name'],
                '文件路径': file_info['path'],
                '文件大小(MB)': round(size_mb, 2),
                '创建时间': created_time,
                '修改时间': modified_time,
                '分享链接': file_info['share_link'],
                '链接ID': file_info['link_id'],
                '链接过期时间': file_info['link_expire'],
                '下载次数': file_info.get('link_downloads', ''),
                '流量(字节)': file_info.get('link_traffic', ''),
                '文件ID': file_info['id']
            })
        
        # 创建DataFrame
        df = pd.DataFrame(df_data)
        
        # 保存到Excel
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='文件列表', index=False)
            
            # 获取工作簿和工作表
            workbook = writer.book
            worksheet = writer.sheets['文件列表']
            
            # 调整列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        print(f"✅ 成功保存 {len(df_data)} 个文件的信息到 {filename}")
        
        # 显示统计信息
        total_files = len(df_data)
        files_with_links = len([f for f in demo_files if f['share_link']])
        total_size_gb = sum([f['size'] for f in demo_files]) / (1024 * 1024 * 1024)
        
        print(f"\n=== 演示统计信息 ===")
        print(f"总文件数: {total_files}")
        print(f"有分享链接的文件: {files_with_links}")
        print(f"总文件大小: {total_size_gb:.2f} GB")
        
        return True
        
    except Exception as e:
        print(f"❌ 保存Excel文件时出错: {e}")
        return False

def main():
    """演示主函数"""
    print("=== pCloud文件批量提取工具 - 演示模式 ===")
    print("此演示会生成模拟数据并导出为Excel文件，用于测试程序功能")
    print()
    
    # 生成演示数据
    demo_files = create_demo_data()
    print(f"✅ 生成了 {len(demo_files)} 个演示文件")
    
    # 设置输出文件名
    output_file = input("\n请输入输出Excel文件名 (默认: demo_pcloud_files.xlsx): ").strip()
    if not output_file:
        output_file = "demo_pcloud_files.xlsx"
    
    if not output_file.endswith('.xlsx'):
        output_file += '.xlsx'
    
    # 保存到Excel
    success = save_demo_to_excel(demo_files, output_file)
    
    if success:
        print(f"\n🎉 演示完成！")
        print(f"📁 演示文件: {output_file}")
        print(f"\n这个Excel文件展示了真实程序的输出格式。")
        print(f"要提取真实的pCloud数据，请运行: python pcloud_extract_links.py")
    else:
        print(f"\n❌ 演示失败")

if __name__ == "__main__":
    main() 