# pCloud文件批量提取工具 - 路径选择功能说明

## 🎯 新功能：指定路径扫描

在 v2.2.0 版本中，我们新增了**路径选择功能**，让您可以精准扫描特定文件夹，而不用每次都扫描整个pCloud。

## 🚀 使用方法

### 启动程序
```bash
python pcloud_extract_links.py
```

### 选择扫描范围
当程序询问扫描范围时：
```
扫描范围选择:
1. 扫描整个pCloud (默认)
2. 选择指定文件夹
请选择 (1/2，默认1):
```

- **选择1或直接按回车**：扫描整个pCloud（传统模式）
- **选择2**：进入交互式文件夹浏览器

## 📁 交互式文件夹浏览器

选择选项2后，您将看到类似以下的界面：

```
📁 当前路径: /
   包含 3 个子文件夹，2 个文件

🎯 路径选择选项:
  0. 选择当前路径 (/)
  📁 子文件夹:
     1. 文档/
     2. 照片/
     3. 音乐/
  q. 返回上级或退出

请选择 (0-3, b, q):
```

### 操作说明

| 输入 | 功能 |
|------|------|
| **0** | 选择当前路径（开始扫描该文件夹） |
| **1-9** | 进入对应编号的子文件夹 |
| **b** | 返回上级文件夹 |
| **q** | 退出选择（返回全盘扫描） |

## 📋 使用示例

### 示例1：扫描特定文件夹
假设您只想扫描"文档"文件夹：

1. 选择扫描范围：输入 `2`
2. 看到根目录文件夹列表，输入 `1`（选择文档/）
3. 进入文档文件夹，如果这就是您要的路径，输入 `0`
4. 程序开始扫描 `/文档/` 路径

### 示例2：深入子文件夹
假设您要扫描"文档/工作/项目"：

1. 选择扫描范围：输入 `2`
2. 根目录：输入 `1`（进入文档/）
3. 文档目录：输入相应数字（进入工作/）
4. 工作目录：输入相应数字（进入项目/）
5. 项目目录：输入 `0`（选择当前路径）

## 🎯 适用场景

### 什么时候使用路径选择？

✅ **推荐使用场景**：
- 只需要处理特定项目的文件
- pCloud中文件很多，想节省时间
- 需要生成特定文件夹的分享链接清单
- 定期处理某个工作目录

❌ **不建议使用场景**：
- 需要完整的文件清单
- 不确定文件分布在哪些文件夹
- 首次使用，想了解整体文件结构

## 📊 输出结果

无论选择哪种扫描方式，Excel文件都包含相同的列：

| 列名 | 说明 |
|------|------|
| 文件名 | 文件名称 |
| 文件路径 | **完整路径**（从根目录开始） |
| 文件大小(MB) | 文件大小 |
| 创建时间 | 文件创建时间 |
| 修改时间 | 文件修改时间 |
| 分享链接 | 公共分享链接 |
| 链接ID | 分享链接ID |
| 下载次数 | 下载统计 |
| 流量 | 流量统计（MB格式） |
| 文件ID | pCloud文件ID |

> **注意**：即使选择子文件夹扫描，文件路径仍然显示完整的路径（如 `/文档/项目/文件.txt`）

## 💡 使用技巧

### 1. 路径导航
- 使用数字键快速选择文件夹
- 使用 `b` 键方便地返回上级
- 使用 `q` 键随时退出

### 2. 路径确认
- 程序会显示当前路径和文件统计
- 选择前可以看到文件夹包含的文件数量
- 确保选择正确的路径再按 `0`

### 3. 批处理脚本
- `直接启动.bat` 和 `创建分享链接.bat` 默认使用全盘扫描
- 如需指定路径，建议直接运行 Python 程序

## 🔧 故障排除

### 无法访问某个文件夹
- 检查该文件夹是否存在权限限制
- 确认您有访问该文件夹的权限

### 文件夹列表为空
- 该文件夹可能只包含文件，没有子文件夹
- 可以选择 `0` 扫描当前文件夹中的文件

### 意外退出选择
- 重新运行程序
- 选择扫描范围时输入 `2` 重新开始

---

**使用路径选择功能，让您的pCloud文件管理更加精准高效！** 🎯 