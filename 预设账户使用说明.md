# pCloud文件批量提取工具 - 预设账户版使用说明

## 📧 预设账户信息

此版本已经预设了以下pCloud账户信息：
- **用户名**: <EMAIL>
- **密码**: abcd12345678

## 🚀 快速启动

### 方式一：双击启动（推荐）
直接双击 `直接启动.bat` 文件，程序会：
1. 自动检查Python环境
2. 安装必要的依赖包
3. 启动提取工具并自动使用预设账户

### 方式二：命令行启动
```bash
python pcloud_extract_links.py
```
然后在提示时选择使用预设账户（按回车键或输入 'y'）

## 💡 使用流程

1. **启动程序**
   - 程序检测到预设账户信息
   - 提示：`🔐 检测到预设账户信息: <EMAIL>`

2. **确认使用预设账户**
   - 提示：`是否使用预设账户？(y/n，默认y):`
   - 直接按回车键或输入 'y' 使用预设账户
   - 输入 'n' 可以手动输入其他账户信息

3. **选择扫描范围**
   - 提示：`扫描范围选择: 1. 扫描整个pCloud (默认) 2. 选择指定文件夹`
   - 选择1或直接按回车：扫描整个pCloud
   - 选择2：进入交互式文件夹浏览器，选择特定路径

4. **设置输出文件名**
   - 提示：`请输入输出Excel文件名 (默认: pCloud链接.xlsx):`
   - 直接按回车使用默认文件名，或输入自定义名称
   - 文件将自动保存到：`/Users/<USER>/Desktop/Input/`

5. **选择是否创建分享链接**
   - 如果有文件没有分享链接，程序会询问是否创建
   - 默认选择"y"（创建），直接按回车即可
   - 输入"n"可跳过创建分享链接

6. **等待提取完成**
   - 程序会自动扫描选择的路径并匹配分享链接
   - 显示实时进度和统计信息

## 📊 输出结果

程序会生成一个Excel文件，包含：
- 文件名和完整路径
- 文件大小（MB）
- 创建和修改时间
- 分享链接（如果存在）
- 链接统计信息（下载次数、流量）

## ⚠️ 安全提示

### 重要注意事项：
1. **账户信息已硬编码在程序中**
2. **请勿将此程序分享给他人**
3. **请勿上传到公共代码仓库**
4. **使用完毕后建议删除预设信息**

### 清除预设信息：
如需清除预设账户信息，请编辑 `pcloud_extract_links.py` 文件，将以下行修改为：
```python
PRESET_USERNAME = ""
PRESET_PASSWORD = ""
```

## 🔧 故障排除

### 连接问题
- 确保网络连接正常
- 验证pCloud服务可访问
- 检查预设账户信息是否正确

### 权限问题
- 确认账户有权限访问文件
- 检查是否有二次验证设置

### 程序错误
- 确保Python版本为3.9或更高
- 检查依赖包是否正确安装
- 重新运行 `直接启动.bat`

## 📈 功能特点

- ✅ **一键启动**：无需手动输入账户信息
- ✅ **自动化处理**：从登录到导出Excel全自动
- ✅ **实时进度**：显示扫描进度和统计信息
- ✅ **智能匹配**：自动匹配文件与分享链接
- ✅ **格式优化**：生成格式美观的Excel文件

## 📝 使用技巧

1. **大量文件处理**：如果您的pCloud中有很多文件，建议在网络状况良好时运行
2. **重复运行**：可以多次运行程序更新数据
3. **文件筛选**：生成Excel后可使用Excel的筛选功能查找特定文件
4. **备份结果**：建议定期备份生成的Excel文件

## 🎯 适用场景

- 📁 **文件管理**：整理和管理pCloud中的大量文件
- 🔗 **链接整理**：统计和管理所有分享链接
- 📊 **数据统计**：分析文件分布和存储使用情况
- 🔍 **文件查找**：快速定位特定文件的位置和信息

---

**享受便捷的pCloud文件管理体验！** 🎉 