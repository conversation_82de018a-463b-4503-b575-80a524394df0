@echo off
chcp 65001 >nul
echo ========================================
echo pCloud文件批量提取工具 - 自动创建分享链接
echo ========================================
echo.

echo 🔗 此脚本将会：
echo   1. 扫描您的pCloud文件
echo   2. 为没有分享链接的文件自动创建分享链接
echo   3. 导出包含所有分享链接的Excel文件
echo.

echo ⚠️  注意：创建分享链接可能需要较长时间
echo.

set /p confirm="确认要继续吗？(Y/N): "
if /i "%confirm%" neq "Y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python，请先安装Python 3.9或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 正在安装必要的依赖包...
pip install -r requirements.txt >nul 2>&1
if errorlevel 1 (
    echo ❌ 依赖包安装失败，请检查网络连接
    echo 正在尝试手动安装...
    pip install requests requests-toolbelt pandas openpyxl
)

echo ✅ 依赖包准备完成
echo.

echo 🚀 正在启动pCloud文件提取工具...
echo 📧 预设账户: <EMAIL>
echo 🔗 将自动为所有文件创建分享链接
echo.

(echo y & echo 1 & echo & echo) | python pcloud_extract_links.py

echo.
echo 程序执行完成，按任意键退出...
pause >nul 