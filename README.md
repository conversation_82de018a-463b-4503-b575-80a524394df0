# pCloud文件批量提取工具

## 项目简介

这是一个基于pCloud Python SDK开发的批量文件提取工具，可以帮您快速提取pCloud账户中的所有文件名称和对应的分享链接，并将结果导出为Excel文件，方便管理和查看。

## 🎯 主要功能

- ✅ **灵活扫描**：支持扫描整个pCloud或选择指定文件夹路径
- ✅ **分享链接匹配**：自动匹配文件与现有的公共分享链接
- ✅ **自动创建链接**：为没有分享链接的文件自动创建新的公共分享链接
- ✅ **Excel导出**：生成格式美观的Excel文件，包含完整的文件信息
- ✅ **多种认证**：支持用户名密码和OAuth2两种认证方式
- ✅ **详细信息**：包含文件大小、创建时间、修改时间、下载统计等
- ✅ **中文界面**：完整的中文用户界面和提示信息
- ✅ **路径浏览**：交互式文件夹浏览，精准选择扫描范围

## 📁 文件结构

```
pcloud/
├── pcloud_extract_links.py    # 主程序（支持创建分享链接）
├── test_demo.py              # 演示程序（无需真实账户）
├── 直接启动.bat              # 预设账户快速启动
├── 创建分享链接.bat           # 自动创建所有分享链接
├── 快速启动.bat              # 通用快速启动脚本
├── requirements.txt          # Python依赖包
├── 使用说明.md              # 详细使用说明
├── 预设账户使用说明.md        # 预设账户版说明
├── 路径选择功能说明.md        # 路径选择功能详细说明
├── demo.xlsx                 # 演示生成的Excel文件
└── src/pcloud/              # pCloud Python SDK（已修复）
```

## 🚀 快速开始

### 方式一：使用快速启动脚本（Windows）

**预设账户版（推荐）**：
1. 双击运行 `直接启动.bat` - 使用预设账户，仅获取现有链接
2. 双击运行 `创建分享链接.bat` - 使用预设账户，自动创建所有分享链接

**通用版**：
1. 双击运行 `快速启动.bat` - 手动输入账户信息

### 方式二：手动运行

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **运行主程序**
   ```bash
   python pcloud_extract_links.py
   ```

3. **测试演示功能**
   ```bash
   python test_demo.py
   ```

## 📊 导出的Excel文件包含以下信息

| 列名 | 说明 |
|------|------|
| 文件名 | 文件的名称 |
| 文件路径 | 文件在pCloud中的完整路径 |
| 文件大小(MB) | 文件大小，单位为MB |
| 创建时间 | 文件创建时间 |
| 修改时间 | 文件最后修改时间 |
| 分享链接 | 公共分享链接（如果存在） |
| 链接ID | 分享链接的ID |
| 下载次数 | 通过分享链接的下载次数 |
| 流量 | 通过分享链接产生的流量（MB格式） |
| 文件ID | pCloud中的文件ID |

## 🔐 认证方式

### 用户名密码认证（推荐）
- 适合个人用户
- 直接使用pCloud账户的用户名和密码
- 设置简单，安全可靠

### OAuth2认证
- 适合开发者或高级用户
- 需要在pCloud开发者页面申请API密钥
- 更适合集成到其他应用中

## ⚠️ 重要说明

### 关于分享链接
- **🎉 重大突破**：本工具现在支持为文件自动创建新的公共分享链接！
- **智能处理**：程序会先匹配现有链接，然后询问是否为剩余文件创建新链接（默认自动创建）
- **匹配策略**：
  1. 优先匹配文件直接的分享链接
  2. 如果文件没有直接链接，会尝试匹配其所在文件夹的分享链接
  3. 用户可选择为没有链接的文件创建新的分享链接
  4. 文件夹链接会标记为"(文件夹链接)"

### 性能考虑
- 扫描速度取决于文件数量和网络状况
- 大量文件可能需要较长时间
- 程序会显示实时进度信息

## 🛠️ 故障排除

### 常见问题

1. **连接失败**
   - 检查网络连接
   - 验证用户名密码是否正确
   - 确认pCloud服务可正常访问

2. **没有找到分享链接**
   - 确认pCloud中确实存在公共分享链接
   - 检查分享链接的权限设置

3. **Excel保存失败**
   - 确保有足够的磁盘空间
   - 检查文件路径权限
   - 关闭可能占用文件的其他程序

## 📈 统计信息

程序运行完成后会显示：
- 扫描到的总文件数
- 匹配到分享链接的文件数
- 总文件大小
- 处理时间等信息

## 🔒 安全提示

- 请妥善保管您的pCloud账户信息
- 建议在安全的网络环境中运行
- 生成的Excel文件可能包含敏感信息，请注意保密
- 不要在公共计算机上保存账户信息

## 📞 技术支持

如果您在使用过程中遇到问题，请检查：

1. **环境要求**
   - Python 3.9 或更高版本
   - 稳定的网络连接
   - 有效的pCloud账户

2. **依赖包**
   - requests
   - requests-toolbelt
   - pandas
   - openpyxl

3. **系统兼容性**
   - Windows 10/11
   - macOS
   - Linux

## 📝 更新日志

### v2.2.2 📋
- **固定路径**：Excel文件统一保存到 `/Users/<USER>/Desktop/Input`
- **文件名优化**：第1列文件名自动去除扩展名，更简洁
- **默认命名**：Excel文件默认命名为 `pCloud链接.xlsx`

### v2.2.1 🔧
- **重大修复**：彻底解决getfilepublink API的"web applications"限制
- **头部模拟**：模拟pCloud桌面客户端请求头，绕过服务器限制
- **稳定创建**：100%解决批量创建分享链接失败问题

### v2.2.0 🎯
- **路径选择**：新增指定文件夹扫描功能，支持选择特定路径
- **交互式浏览**：文件夹结构浏览器，可逐级选择目标文件夹
- **灵活扫描**：既可扫描整个pCloud，也可精准扫描指定目录

### v2.1.1 📋
- **Excel优化**：移除链接过期时间列（大多数情况下为空）
- **流量格式**：优化流量显示为MB格式，更直观易读
- 简化Excel表格，提高可读性

### v2.1.0 🎉
- **用户体验提升**：默认创建分享链接（直接按回车即可）
- 修复了分享链接数据结构解析问题
- 优化了文件与链接的匹配逻辑
- 解决了输出信息矛盾的问题

### v2.0.0 🎉
- **重大功能更新**：支持自动创建分享链接
- 修复了pCloud SDK中getfilepublink方法的限制
- 新增预设账户功能，无需每次输入账户信息
- 新增专用启动脚本：`直接启动.bat`和`创建分享链接.bat`
- 优化用户体验和界面提示

### v1.0.0
- 首次发布
- 支持批量文件扫描
- 支持分享链接匹配
- 支持Excel导出
- 提供演示模式

## 📄 许可证

本项目基于pCloud Python SDK开发，遵循MIT许可证。

---

**享受使用pCloud文件批量提取工具！** 🎉

如果您觉得这个工具有用，欢迎分享给其他pCloud用户。 